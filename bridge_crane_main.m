% Description: 多层优化架构实现双吊桥架桥机智能化控制

clear; clc; close all;

%% 系统初始化
% 加载系统配置
config = initializeSystemConfig();

% 初始化环境模型
env = initializeEnvironment();

% 初始化架桥机模型
crane = initializeCraneModel();

% 初始化GPR代理模型
gprModel = initializeGPRModel();

%% 主优化循环
fprintf('========== 双吊桥架桥机时空耦合优化系统 ==========\n');
fprintf('目标构件类型: %s\n', config.component_type);
fprintf('优化目标: 时间最短、能耗最低、安全性最高\n');
fprintf('开始多目标优化...\n\n');

% GA优化参数设置
ga_options = optimoptions('gamultiobj', ...
    'PopulationSize', config.ga.population_size, ...
    'MaxGenerations', config.ga.max_generations, ...
    'UseParallel', true, ...
    'Display', 'iter', ...
    'PlotFcn', {@gaplotpareto, @gaplotdistance});

% 定义决策变量范围
nvars = config.ga.nvars;
lb = config.ga.lb;
ub = config.ga.ub;

% 定义适应度函数（使用GPR代理模型）
fitnessFcn = @(x) evaluateFitnessWithGPR(x, gprModel, config, env, crane);

% 运行多目标遗传算法
tic;
[x_opt, fval_opt, exitflag, output] = gamultiobj(fitnessFcn, nvars, ...
    [], [], [], [], lb, ub, [], ga_options);
optimization_time = toc;

fprintf('\n优化完成！用时: %.2f秒\n', optimization_time);
fprintf('Pareto前沿解数量: %d\n', size(x_opt, 1));

%% 选择最优解并生成精确轨迹
% 根据权重选择折中解
weights = [0.4, 0.3, 0.3]; % [时间, 能耗, 安全性]权重
best_idx = selectBestSolution(fval_opt, weights);
best_solution = x_opt(best_idx, :);

fprintf('\n生成精确控制轨迹...\n');
[trajectory, performance] = generatePreciseTrajectory(best_solution, config, env, crane);

%% 结果可视化
visualizeResults(trajectory, performance, env, crane);

%% 输出控制指令序列
control_commands = generateControlCommands(trajectory, crane);
exportControlCommands(control_commands, 'control_sequence.csv');

fprintf('\n控制指令已导出至: control_sequence.csv\n');
fprintf('系统运行完成！\n');

%% ========== 核心函数实现 ==========

function config = initializeSystemConfig()
    % 系统配置初始化
    config = struct();
    
    % 构件类型选择
    config.component_type = 'beam'; % 'column', 'cap_beam', 'beam'
    
    % GA参数
    config.ga.population_size = 100;
    config.ga.max_generations = 50;
    
    % 染色体编码维度
    n_anchors = 5; % 路径锚点数
    m_phases = 10; % 相位控制点数
    config.ga.nvars = 3*n_anchors + 2*m_phases + 3; % [锚点, 相位, 权重]
    
    % 决策变量边界
    config.ga.lb = [repmat([-50, -50, 0], 1, n_anchors), ...
                    repmat([-pi, -pi], 1, m_phases), ...
                    0, 0, 0];
    config.ga.ub = [repmat([50, 50, 30], 1, n_anchors), ...
                    repmat([pi, pi], 1, m_phases), ...
                    1, 1, 1];
    
    % 规划参数
    config.rrt.max_iter = 1000;
    config.rrt.step_size = 0.5;
    config.rrt.goal_bias = 0.1;
    
    % QP求解器参数
    config.qp.dt = 0.1; % 时间步长
    config.qp.horizon = 100; % 预测时域
    
    % 代理模型参数
    config.gpr.update_interval = 10; % 每10代更新GPR
    config.gpr.n_samples = 50; % 精确评估样本数
end

function env = initializeEnvironment()
    % 环境模型初始化
    env = struct();
    
    % 工作空间边界
    env.workspace = [-60, 60; -60, 60; 0, 40]; % [x_min, x_max; y_min, y_max; z_min, z_max]
    
    % 障碍物定义
    env.obstacles = [];
    env.obstacles(1) = createObstacle([10, 0, 0], [5, 20, 15], 'box');
    env.obstacles(2) = createObstacle([-15, 10, 0], [8, 8, 20], 'box');
    
    % 目标位置
    env.start_pos = [0, -40, 5];
    env.goal_pos = [0, 40, 10];
end

function crane = initializeCraneModel()
    % 架桥机模型初始化
    crane = struct();
    
    % 机构参数
    crane.main_cart.max_vel = 2.0; % m/s
    crane.main_cart.max_acc = 0.5; % m/s^2
    
    crane.small_cart.max_vel = 1.5; % m/s
    crane.small_cart.max_acc = 0.3; % m/s^2
    
    crane.hoist.max_vel = 0.5; % m/s
    crane.hoist.max_acc = 0.2; % m/s^2
    
    crane.rotator.max_vel = 0.5; % rad/s
    crane.rotator.max_acc = 0.2; % rad/s^2
    
    % 双吊钩参数
    crane.hook1.workspace = [-30, 30; -50, 50; 0, 30];
    crane.hook2.workspace = [-30, 30; -50, 50; 0, 30];
    crane.hook_distance = 15; % 前后吊钩间距
    
    % 负载参数
    crane.load.mass = 50000; % kg
    crane.load.dimensions = [12, 3, 1.5]; % 梁片尺寸
end

function gprModel = initializeGPRModel()
    % 初始化GPR代理模型
    gprModel = struct();
    
    % 生成初始训练数据
    n_init = 100;
    X_train = rand(n_init, 38) .* 100 - 50; % 随机采样
    Y_train = zeros(n_init, 4); % [时间, 能耗, 稳定性, 可行性]
    
    % 评估初始样本
    parfor i = 1:n_init
        Y_train(i, :) = evaluatePrecise(X_train(i, :));
    end
    
    % 训练GPR模型
    gprModel.time = fitrgp(X_train, Y_train(:, 1), 'KernelFunction', 'squaredexponential');
    gprModel.energy = fitrgp(X_train, Y_train(:, 2), 'KernelFunction', 'squaredexponential');
    gprModel.stability = fitrgp(X_train, Y_train(:, 3), 'KernelFunction', 'squaredexponential');
    gprModel.feasibility = fitrgp(X_train, Y_train(:, 4), 'KernelFunction', 'squaredexponential');
    
    % 存储训练数据
    gprModel.X_train = X_train;
    gprModel.Y_train = Y_train;
    gprModel.generation = 0;
end

function fitness = evaluateFitnessWithGPR(x, gprModel, config, env, crane)
    % 使用GPR代理模型评估适应度
    persistent eval_count generation;
    
    if isempty(eval_count)
        eval_count = 0;
        generation = 0;
    end
    
    eval_count = eval_count + 1;
    
    % 检查是否需要更新GPR
    if mod(eval_count, config.ga.population_size) == 0
        generation = generation + 1;
        if mod(generation, config.gpr.update_interval) == 0
            gprModel = updateGPRModel(gprModel, config);
        end
    end
    
    % GPR预测
    [time_pred, ~, time_ci] = predict(gprModel.time, x);
    [energy_pred, ~, energy_ci] = predict(gprModel.energy, x);
    [stability_pred, ~, stability_ci] = predict(gprModel.stability, x);
    [feasibility_pred, ~] = predict(gprModel.feasibility, x);
    
    % 处理不可行解
    if feasibility_pred < 0.5
        fitness = [1e6, 1e6, 1e6]; % 惩罚值
        return;
    end
    
    % 返回多目标适应度值
    fitness = [time_pred, energy_pred, -stability_pred]; % 最小化时间和能耗，最大化稳定性
end

function Y = evaluatePrecise(x)
    % 精确评估函数（调用RRT*和QP）
    % 这里简化实现，实际应调用完整的规划模块
    
    % 解码染色体
    [anchors, phases, weights] = decodeChromosome(x);
    
    % RRT*路径规划
    path = planRRTStar(anchors);
    
    if isempty(path)
        Y = [1e6, 1e6, 0, 0]; % 不可行解
        return;
    end
    
    % QP轨迹优化
    [trajectory, metrics] = optimizeTrajectoryQP(path, phases, weights);
    
    % 返回评估指标
    Y = [metrics.time, metrics.energy, metrics.stability, 1];
end

function [anchors, phases, weights] = decodeChromosome(x)
    % 解码GA染色体
    n_anchors = 5;
    m_phases = 10;
    
    % 提取锚点
    anchor_data = x(1:3*n_anchors);
    anchors = reshape(anchor_data, 3, n_anchors)';
    
    % 提取相位
    phase_data = x(3*n_anchors+1:3*n_anchors+2*m_phases);
    phases = reshape(phase_data, 2, m_phases)';
    
    % 提取权重
    weights = x(end-2:end);
    weights = weights / sum(weights); % 归一化
end

function best_idx = selectBestSolution(fval, weights)
    % 根据权重选择最佳折中解
    normalized_fval = zeros(size(fval));
    for i = 1:size(fval, 2)
        normalized_fval(:, i) = (fval(:, i) - min(fval(:, i))) / ...
                                (max(fval(:, i)) - min(fval(:, i)) + eps);
    end
    
    weighted_sum = normalized_fval * weights';
    [~, best_idx] = min(weighted_sum);
end

function obstacle = createObstacle(center, size, type)
    % 创建障碍物
    obstacle = struct();
    obstacle.center = center;
    obstacle.size = size;
    obstacle.type = type;
    obstacle.vertices = generateObstacleVertices(center, size, type);
end

function vertices = generateObstacleVertices(center, size, type)
    % 生成障碍物顶点
    if strcmp(type, 'box')
        dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;
        vertices = [
            center + [-dx, -dy, -dz];
            center + [dx, -dy, -dz];
            center + [dx, dy, -dz];
            center + [-dx, dy, -dz];
            center + [-dx, -dy, dz];
            center + [dx, -dy, dz];
            center + [dx, dy, dz];
            center + [-dx, dy, dz];
        ];
    end
end